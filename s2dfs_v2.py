
# -*- coding: utf-8 -*-

import numpy as np
import matplotlib.pyplot as plt
from scipy import linalg
import h5py
from joblib import Parallel, delayed
import multiprocessing
import time

# Start timer
start_time = time.time()

# 初始化并行池
# Python 中没有直接等效的 parpool，我们使用 joblib 的 Parallel
num_cores = 12

# ================== 一、常量和转换因子 ==================
CmeV = 0.124 / 1000  # cm^-1 to eV
wlam = 1239.8424121  # nm <--> eV
dh = 1.5193  # 10/hbar
Deb = 3.33564e-30  # Debye to C·m
Ev = 1.602e-19  # eV to J
rk = 4  # RK4 order

# ================== 二、脉冲参数 ==================
ch2 = 0
ch3 = 0  # 无啁啾
TDp = 50  # fs, intensity FWHM
TDe = TDp * np.sqrt(2)  # fs, amplitude FWHM
coeff = 15  # 截断系数
WWp = np.sqrt(8 * np.log(2)) / TDp * 5
stWW = WWp / 200
NWW = 2 * round(WWp / stWW) + 1
WDp = wlam / 2.38  # nm, ~2.38 eV
wp = 2.38  # eV
Nfi = 3  # 相位循环
FiAm = 0.009843  # eV, ~5 μJ/cm^2

# ================== 三、系统哈密顿量 ==================
N = 2
NN = 4  # 两个二能级，4态
E1 = 19032
E2 = 19355  # 2.36 eV, 2.40 eV (cm^-1)
Omega = 0.01 / CmeV  # 耦合 0.01 eV
H0 = np.zeros((NN, NN), dtype=complex)
H0[1, 1] = E1
H0[2, 2] = E2
H0[3, 3] = E1 + E2
H0[1, 2] = Omega
H0[2, 1] = Omega
H0 = H0 * CmeV  # 转为 eV
Z = np.eye(NN)
H0 = H0 - Z * wp  # 旋转参考系
Mu = 6  # Debye
X = np.zeros((NN, NN))
X[0, 1] = Mu
X[0, 2] = Mu
X[1, 3] = Mu
X[2, 3] = Mu
Xd = X.T

# ================== 四、耗散参数 ==================
gamma = 0.001  # ps^-1
gamma_phi = 0.02  # ps^-1, T2* ≈ 50 ps

# ================== 五、时间参数 ==================
max_tau12 = 50  # 100 fs
max_tau34 = 50
step_tau = 2  # fs
maxT = 1
TT = 10  # ps
Delta_t = 0.2  # fs
N_shots = 150  # 逐次次数

# ================== 六、初始化 ==================
Nfi3 = Nfi ** 3
Delta_tR = Delta_t * dh
fi1 = 0
fi2 = 0
fi3 = 0
fi4 = 0
F1 = FiAm
F2 = FiAm
F3 = FiAm
F4 = FiAm
Rho = np.zeros((NN, NN), dtype=complex)
Rho[0, 0] = 1
ttau12 = np.arange(0, max_tau12) * step_tau
ttau34 = np.arange(0, max_tau34) * step_tau
I_avg = np.zeros((max_tau12, max_tau34))
I_var = np.zeros((max_tau12, max_tau34))
Pop_ex = np.zeros((round(10 / Delta_t) + 1, maxT))
Ark = np.zeros((4, 4))
Brk = np.zeros(4)
Crk = np.zeros(4)
Ark[1, 0] = 0.5
Ark[2, 1] = 0.5
Ark[3, 2] = 1
Brk[0] = 1 / 6
Brk[1] = 2 / 6
Brk[2] = 2 / 6
Brk[3] = 1 / 6
Crk[1] = 0.5
Crk[2] = 0.5
Crk[3] = 1

# ================== 七、单独计算时间域布居 ==================
print('Computing time-domain population...')
T = TT
tau12 = 0
tau34 = 0
tau1 = -tau12 - T - tau34
tau2 = -T - tau34
tau3 = -tau34
tau4 = 0
tau0 = -coeff * TDe - tau12 - T - tau34
maxJ = round((2 * coeff * TDe + max_tau12 * step_tau +
              max_tau34 * step_tau + TT) / Delta_t) + 1
maxJ2 = 2 * maxJ + 3

# 脉冲包络
Pulse1 = np.zeros(maxJ2, dtype=complex)
Pulse2 = np.zeros(maxJ2, dtype=complex)
Pulse3 = np.zeros(maxJ2, dtype=complex)
Pulse4 = np.zeros(maxJ2, dtype=complex)
TRK = np.zeros(maxJ2)
for jt2 in range(maxJ2):
    tt2 = tau0 + jt2 * Delta_t / 2
    TRK[jt2] = tt2
    A1 = abs(tt2 - tau1) <= coeff * TDe
    A2 = abs(tt2 - tau2) <= coeff * TDe
    A3 = abs(tt2 - tau3) <= coeff * TDe
    A4 = abs(tt2 - tau4) <= coeff * TDe
    for iw in range(int(NWW)):
        WW = iw * stWW - WWp
        EW = np.exp(-TDp**2 / (8 * np.log(2)) * WW**2)
        Pulse1[jt2] += A1 * EW * np.exp(1j * WW * (tt2 - tau1))
        Pulse2[jt2] += A2 * EW * np.exp(1j * WW * (tt2 - tau2))
        Pulse3[jt2] += A3 * EW * np.exp(1j * WW * (tt2 - tau3))
        Pulse4[jt2] += A4 * EW * np.exp(1j * WW * (tt2 - tau4))

Pu1 = max(np.real(Pulse1))
Pulse1 = Pulse1 / Pu1
Pu2 = max(np.real(Pulse2))
Pulse2 = Pulse2 / Pu2
Pu3 = max(np.real(Pulse3))
Pulse3 = Pulse3 / Pu3
Pu4 = max(np.real(Pulse4))
Pulse4 = Pulse4 / Pu4

# 时间演化
Rho_t = Rho.copy()
tm = np.zeros(maxJ)
for jt in range(min(maxJ, round(10 / Delta_t) + 1)):
    tm[jt] = tau0 + jt * Delta_t
    tc = tm[jt]
    g1 = F1 * Pulse1[2 * jt]
    g2 = F2 * Pulse2[2 * jt]
    g3 = F3 * Pulse3[2 * jt]
    g4 = F4 * Pulse4[2 * jt]
    phase1 = (wp * tc) * dh - fi1
    phase2 = (wp * tc) * dh - fi2
    phase3 = (wp * tc) * dh - fi3
    phase4 = (wp * tc) * dh - fi4
    FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
         np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
    FXd = np.conj(FX)
    Ht = H0 - X * FX - Xd * FXd

    K1 = -1j * (Ht @ Rho_t - Rho_t @ Ht)
    for i in range(N):
        sig_minus = np.zeros((NN, NN), dtype=complex)
        if i == 0:
            sig_minus[0, 1] = 1
        else:
            sig_minus[0, 2] = 1
        sig_plus = sig_minus.T
        sig_z = sig_plus @ sig_minus
        K1 = K1 + gamma * (sig_minus @ Rho_t @ sig_plus -
                           0.5 * (sig_plus @ sig_minus @ Rho_t + Rho_t @ sig_plus @ sig_minus)) + \
             gamma_phi * (sig_z @ Rho_t @ sig_z -
                          0.5 * (sig_z @ sig_z @ Rho_t + Rho_t @ sig_z @ sig_z))

    Rho_temp = Rho_t + Delta_tR * Ark[1, 0] * K1
    tc_temp = tc + Delta_t * Crk[1]
    g1 = F1 * Pulse1[2 * jt]
    g2 = F2 * Pulse2[2 * jt]
    g3 = F3 * Pulse3[2 * jt]
    g4 = F4 * Pulse4[2 * jt]
    phase1 = (wp * tc_temp) * dh - fi1
    phase2 = (wp * tc_temp) * dh - fi2
    phase3 = (wp * tc_temp) * dh - fi3
    phase4 = (wp * tc_temp) * dh - fi4
    FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
         np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
    FXd = np.conj(FX)
    Ht = H0 - X * FX - Xd * FXd
    K2 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
    for i in range(N):
        sig_minus = np.zeros((NN, NN), dtype=complex)
        if i == 0:
            sig_minus[0, 1] = 1
        else:
            sig_minus[0, 2] = 1
        sig_plus = sig_minus.T
        sig_z = sig_plus @ sig_minus
        K2 = K2 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                           0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
             gamma_phi * (sig_z @ Rho_temp @ sig_z -
                          0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

    Rho_temp = Rho_t + Delta_tR * (Ark[2, 0] * K1 + Ark[2, 1] * K2)
    tc_temp = tc + Delta_t * Crk[2]
    g1 = F1 * Pulse1[2 * jt]
    g2 = F2 * Pulse2[2 * jt]
    g3 = F3 * Pulse3[2 * jt]
    g4 = F4 * Pulse4[2 * jt]
    phase1 = (wp * tc_temp) * dh - fi1
    phase2 = (wp * tc_temp) * dh - fi2
    phase3 = (wp * tc_temp) * dh - fi3
    phase4 = (wp * tc_temp) * dh - fi4
    FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
         np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
    FXd = np.conj(FX)
    Ht = H0 - X * FX - Xd * FXd
    K3 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
    for i in range(N):
        sig_minus = np.zeros((NN, NN), dtype=complex)
        if i == 0:
            sig_minus[0, 1] = 1
        else:
            sig_minus[0, 2] = 1
        sig_plus = sig_minus.T
        sig_z = sig_plus @ sig_minus
        K3 = K3 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                           0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
             gamma_phi * (sig_z @ Rho_temp @ sig_z -
                          0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

    Rho_temp = Rho_t + Delta_tR * (Ark[3, 0] * K1 + Ark[3, 1] * K2 + Ark[3, 2] * K3)
    tc_temp = tc + Delta_t * Crk[3]
    g1 = F1 * Pulse1[2 * jt]
    g2 = F2 * Pulse2[2 * jt]
    g3 = F3 * Pulse3[2 * jt]
    g4 = F4 * Pulse4[2 * jt]
    phase1 = (wp * tc_temp) * dh - fi1
    phase2 = (wp * tc_temp) * dh - fi2
    phase3 = (wp * tc_temp) * dh - fi3
    phase4 = (wp * tc_temp) * dh - fi4
    FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
         np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
    FXd = np.conj(FX)
    Ht = H0 - X * FX - Xd * FXd
    K4 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
    for i in range(N):
        sig_minus = np.zeros((NN, NN), dtype=complex)
        if i == 0:
            sig_minus[0, 1] = 1
        else:
            sig_minus[0, 2] = 1
        sig_plus = sig_minus.T
        sig_z = sig_plus @ sig_minus
        K4 = K4 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                           0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
             gamma_phi * (sig_z @ Rho_temp @ sig_z -
                          0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

    Rho_t = Rho_t + Delta_tR * (Brk[0] * K1 + Brk[1] * K2 + Brk[2] * K3 + Brk[3] * K4)
    Pop_ex[jt, 0] = np.real(Rho_t[1, 1] + Rho_t[2, 2] + 2 * Rho_t[3, 3])

# ================== 八、主循环：并行FR-2D ==================
print(f'Computing FR-2D, T = {T} ps')
I_shots = np.zeros((max_tau12, max_tau34, N_shots))

def compute_tau12(itau12):
    tau12 = itau12 * step_tau
    I_shots_temp = np.zeros((max_tau34, N_shots))

    for itau34 in range(max_tau34):
        tau34 = itau34 * step_tau

        # 脉冲时间
        tau1 = -tau12 - T - tau34
        tau2 = -T - tau34
        tau3 = -tau34
        tau4 = 0

        # 时间范围
        tau0 = -coeff * TDe - tau12 - T - tau34
        maxJ = round((2 * coeff * TDe + max_tau12 * step_tau +
                      max_tau34 * step_tau + TT) / Delta_t) + 1
        maxJ2 = 2 * maxJ + 3

        # 脉冲包络
        Pulse1 = np.zeros(maxJ2, dtype=complex)
        Pulse2 = np.zeros(maxJ2, dtype=complex)
        Pulse3 = np.zeros(maxJ2, dtype=complex)
        Pulse4 = np.zeros(maxJ2, dtype=complex)
        TRK = np.zeros(maxJ2)

        for jt2 in range(maxJ2):
            tt2 = tau0 + jt2 * Delta_t / 2
            TRK[jt2] = tt2
            A1 = abs(tt2 - tau1) <= coeff * TDe
            A2 = abs(tt2 - tau2) <= coeff * TDe
            A3 = abs(tt2 - tau3) <= coeff * TDe
            A4 = abs(tt2 - tau4) <= coeff * TDe

            for iw in range(int(NWW)):
                WW = iw * stWW - WWp
                EW = np.exp(-TDp**2 / (8 * np.log(2)) * WW**2)
                Pulse1[jt2] += A1 * EW * np.exp(1j * WW * (tt2 - tau1))
                Pulse2[jt2] += A2 * EW * np.exp(1j * WW * (tt2 - tau2))
                Pulse3[jt2] += A3 * EW * np.exp(1j * WW * (tt2 - tau3))
                Pulse4[jt2] += A4 * EW * np.exp(1j * WW * (tt2 - tau4))

        # 归一化
        Pu1 = max(np.real(Pulse1))
        Pulse1 = Pulse1 / Pu1
        Pu2 = max(np.real(Pulse2))
        Pulse2 = Pulse2 / Pu2
        Pu3 = max(np.real(Pulse3))
        Pulse3 = Pulse3 / Pu3
        Pu4 = max(np.real(Pulse4))
        Pulse4 = Pulse4 / Pu4

        # 逐次模拟
        I_temp = np.zeros(N_shots)
        for n in range(N_shots):
            Rho_n = np.zeros((NN, NN), dtype=complex)
            Rho_n[0, 0] = 1
            if n > 0:
                epsilon = 0.15  # 增加扰动
                Rho_rand = np.random.rand(NN, NN) + 1j * np.random.rand(NN, NN)
                Rho_rand = Rho_rand + Rho_rand.T
                Rho_rand = Rho_rand / np.trace(Rho_rand)
                Rho_n = (1 - epsilon) * Rho_n + epsilon * Rho_rand

            Rho_t = Rho_n.copy()
            tm = np.zeros(maxJ)
            for jt in range(maxJ):
                tm[jt] = tau0 + jt * Delta_t
                tc = tm[jt]

                g1 = F1 * Pulse1[2 * jt]
                g2 = F2 * Pulse2[2 * jt]
                g3 = F3 * Pulse3[2 * jt]
                g4 = F4 * Pulse4[2 * jt]
                phase1 = (wp * tc) * dh - fi1
                phase2 = (wp * tc) * dh - fi2
                phase3 = (wp * tc) * dh - fi3
                phase4 = (wp * tc) * dh - fi4
                FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
                     np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
                FXd = np.conj(FX)
                Ht = H0 - X * FX - Xd * FXd

                K1 = -1j * (Ht @ Rho_t - Rho_t @ Ht)
                for i in range(N):
                    sig_minus = np.zeros((NN, NN), dtype=complex)
                    if i == 0:
                        sig_minus[0, 1] = 1
                    else:
                        sig_minus[0, 2] = 1
                    sig_plus = sig_minus.T
                    sig_z = sig_plus @ sig_minus
                    K1 = K1 + gamma * (sig_minus @ Rho_t @ sig_plus -
                                       0.5 * (sig_plus @ sig_minus @ Rho_t + Rho_t @ sig_plus @ sig_minus)) + \
                         gamma_phi * (sig_z @ Rho_t @ sig_z -
                                      0.5 * (sig_z @ sig_z @ Rho_t + Rho_t @ sig_z @ sig_z))

                Rho_temp = Rho_t + Delta_tR * Ark[1, 0] * K1
                tc_temp = tc + Delta_t * Crk[1]
                g1 = F1 * Pulse1[2 * jt]
                g2 = F2 * Pulse2[2 * jt]
                g3 = F3 * Pulse3[2 * jt]
                g4 = F4 * Pulse4[2 * jt]
                phase1 = (wp * tc_temp) * dh - fi1
                phase2 = (wp * tc_temp) * dh - fi2
                phase3 = (wp * tc_temp) * dh - fi3
                phase4 = (wp * tc_temp) * dh - fi4
                FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
                     np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
                FXd = np.conj(FX)
                Ht = H0 - X * FX - Xd * FXd
                K2 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
                for i in range(N):
                    sig_minus = np.zeros((NN, NN), dtype=complex)
                    if i == 0:
                        sig_minus[0, 1] = 1
                    else:
                        sig_minus[0, 2] = 1
                    sig_plus = sig_minus.T
                    sig_z = sig_plus @ sig_minus
                    K2 = K2 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                                       0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
                         gamma_phi * (sig_z @ Rho_temp @ sig_z -
                                      0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

                Rho_temp = Rho_t + Delta_tR * (Ark[2, 0] * K1 + Ark[2, 1] * K2)
                tc_temp = tc + Delta_t * Crk[2]
                g1 = F1 * Pulse1[2 * jt]
                g2 = F2 * Pulse2[2 * jt]
                g3 = F3 * Pulse3[2 * jt]
                g4 = F4 * Pulse4[2 * jt]
                phase1 = (wp * tc_temp) * dh - fi1
                phase2 = (wp * tc_temp) * dh - fi2
                phase3 = (wp * tc_temp) * dh - fi3
                phase4 = (wp * tc_temp) * dh - fi4
                FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
                     np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
                FXd = np.conj(FX)
                Ht = H0 - X * FX - Xd * FXd
                K3 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
                for i in range(N):
                    sig_minus = np.zeros((NN, NN), dtype=complex)
                    if i == 0:
                        sig_minus[0, 1] = 1
                    else:
                        sig_minus[0, 2] = 1
                    sig_plus = sig_minus.T
                    sig_z = sig_plus @ sig_minus
                    K3 = K3 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                                       0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
                         gamma_phi * (sig_z @ Rho_temp @ sig_z -
                                      0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

                Rho_temp = Rho_t + Delta_tR * (Ark[3, 0] * K1 + Ark[3, 1] * K2 + Ark[3, 2] * K3)
                tc_temp = tc + Delta_t * Crk[3]
                g1 = F1 * Pulse1[2 * jt]
                g2 = F2 * Pulse2[2 * jt]
                g3 = F3 * Pulse3[2 * jt]
                g4 = F4 * Pulse4[2 * jt]
                phase1 = (wp * tc_temp) * dh - fi1
                phase2 = (wp * tc_temp) * dh - fi2
                phase3 = (wp * tc_temp) * dh - fi3
                phase4 = (wp * tc_temp) * dh - fi4
                FX = np.exp(1j * phase1) * g1 + np.exp(1j * phase2) * g2 + \
                     np.exp(1j * phase3) * g3 + np.exp(1j * phase4) * g4
                FXd = np.conj(FX)
                Ht = H0 - X * FX - Xd * FXd
                K4 = -1j * (Ht @ Rho_temp - Rho_temp @ Ht)
                for i in range(N):
                    sig_minus = np.zeros((NN, NN), dtype=complex)
                    if i == 0:
                        sig_minus[0, 1] = 1
                    else:
                        sig_minus[0, 2] = 1
                    sig_plus = sig_minus.T
                    sig_z = sig_plus @ sig_minus
                    K4 = K4 + gamma * (sig_minus @ Rho_temp @ sig_plus -
                                       0.5 * (sig_plus @ sig_minus @ Rho_temp + Rho_temp @ sig_plus @ sig_minus)) + \
                         gamma_phi * (sig_z @ Rho_temp @ sig_z -
                                      0.5 * (sig_z @ sig_z @ Rho_temp + Rho_t @ sig_z @ sig_z))

                Rho_t = Rho_t + Delta_tR * (Brk[0] * K1 + Brk[1] * K2 + Brk[2] * K3 + Brk[3] * K4)

            W = 10  # 检测窗口 10 ps
            t_det = tau4
            I_temp[n] = 0
            for jt in range(int(round(t_det / Delta_t)), int(round((t_det + W) / Delta_t)) + 1):
                I_temp[n] += gamma * np.real(Rho_t[1, 1] + Rho_t[2, 2])
            I_shots_temp[itau34, n] = I_temp[n]

    return itau12, I_shots_temp

# 并行计算
results = Parallel(n_jobs=num_cores)(delayed(compute_tau12)(itau12) for itau12 in range(max_tau12))
for itau12, I_shots_temp in results:
    I_shots[itau12, :, :] = I_shots_temp

# 计算均值和方差
for itau12 in range(max_tau12):
    for itau34 in range(max_tau34):
        I_avg[itau12, itau34] = np.mean(I_shots[itau12, itau34, :])
        I_var[itau12, itau34] = np.var(I_shots[itau12, itau34, :])

# ================== 九、2D光谱计算 ==================
max_Wtau = 80
max_Wt = 80
Del = 0.05
step_Wt = 2 * Del / max_Wt
Wt0 = wp - Del
Wtau0 = wp - Del
f2 = 4 * np.log(2)
fw12 = np.sqrt(-f2 * (max_tau12 * step_tau)**2 / np.log(0.05))
fw34 = np.sqrt(-f2 * (max_tau34 * step_tau)**2 / np.log(0.05))
C12 = f2 / (fw12**2 * 1)  # 增强平滑，减少伪影
C34 = f2 / (fw34**2 * 1)
Stauw_avg = np.zeros((max_tau12, max_Wt), dtype=complex)
Sww_avg = np.zeros((max_Wtau, max_Wt), dtype=complex)
Stauw_var = np.zeros((max_tau12, max_Wt), dtype=complex)
Sww_var = np.zeros((max_Wtau, max_Wt), dtype=complex)
Wt = Wt0 + np.arange(max_Wt) * step_Wt
Wtau = Wtau0 + np.arange(max_Wtau) * step_Wt

for j1 in range(max_tau12):
    for j3w in range(max_Wt):
        for j3 in range(max_tau34):
            Wind = np.exp(-C34 * ttau34[j3]**2)
            fi = 1.52 * ttau34[j3] * Wt[j3w]
            Stauw_avg[j1, j3w] += I_avg[j1, j3] * np.exp(1j * fi) * Wind
            Stauw_var[j1, j3w] += I_var[j1, j3] * np.exp(1j * fi) * Wind

for j3w in range(max_Wt):
    for j1w in range(max_Wtau):
        for j1 in range(max_tau12):
            Wind = np.exp(-C12 * ttau12[j1]**2)
            fi = ttau12[j1] * Wtau[j1w] * 1.52
            Sww_avg[j1w, j3w] += Stauw_avg[j1, j3w] * np.exp(-1j * fi) * Wind
            Sww_var[j1w, j3w] += Stauw_var[j1, j3w] * np.exp(-1j * fi) * Wind

Sp2D_avg = -np.real(Sww_avg)
Sp2D_var = np.real(Sww_var)

# ================== 十、可视化 ==================
plt.figure()
plt.plot(np.arange(0, 10 + Delta_t, Delta_t), Pop_ex[:, 0], 'r')
plt.xlabel('Time (ps)')
plt.ylabel('Excited Population')
plt.title('SF Burst')
plt.grid(True)
plt.show()

plt.figure()
X, Y = np.meshgrid(Wtau, Wt)
plt.contourf(X, Y, Sp2D_avg.T, levels=100)
plt.xlim([2.31, 2.45])
plt.ylim([2.31, 2.45])
plt.xlabel(r'$\omega_{\tau}$ (eV)')
plt.ylabel(r'$\omega_{t}$ (eV)')
plt.title('FR-2D: I_{avg}')
plt.colorbar()
plt.contour(X, Y, Sp2D_avg.T, colors='k')
plt.show()

plt.figure()
R = Sp2D_var / (Sp2D_avg**2 + np.finfo(float).eps)
plt.contourf(X, Y, R.T, levels=100)
plt.xlim([2.31, 2.45])
plt.ylim([2.31, 2.45])
plt.xlabel(r'$\omega_{\tau}$ (eV)')
plt.ylabel(r'$\omega_{t}$ (eV)')
plt.title('Normalized Variance R')
plt.colorbar()
plt.contour(X, Y, R.T, colors='k')
plt.show()

plt.figure()
plt.contourf(X, Y, Sp2D_var.T, levels=100)
plt.xlim([2.31, 2.45])
plt.ylim([2.31, 2.45])
plt.xlabel(r'$\omega_{\tau}$ (eV)')
plt.ylabel(r'$\omega_{t}$ (eV)')
plt.title('FR-2D: I_{var}')
plt.colorbar()
plt.show()

# ================== 十一、保存 ==================
with h5py.File('FR2D_CsPbBr3_Fast.mat', 'w') as f:
    f.create_dataset('Sp2D_avg', data=Sp2D_avg)
    f.create_dataset('Sp2D_var', data=Sp2D_var)
    f.create_dataset('R', data=R)
    f.create_dataset('Wtau', data=Wtau)
    f.create_dataset('Wt', data=Wt)
    f.create_dataset('Pop_ex', data=Pop_ex)
    f.create_dataset('ttau12', data=ttau12)
    f.create_dataset('ttau34', data=ttau34)

# 打印运行时间
print(f"Elapsed time: {time.time() - start_time} seconds")