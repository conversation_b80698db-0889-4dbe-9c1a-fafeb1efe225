



% 初始化并行池
delete(gcp('nocreate')); % 关闭现有池
parpool('local', 12);    % 12进程

%% ================== 一、常量和转换因子 ==================
CmeV = 0.124/1000;     % cm^-1 to eV
wlam = 1239.8424121;    % nm <--> eV
dh = 1.5193;            % 10/hbar
Deb = 3.33564e-30;      % Debye to C·m
Ev = 1.602e-19;         % eV to J
rk = 4;                 % RK4 order

%% ================== 二、脉冲参数 ==================
ch2 = 0; ch3 = 0;       % 无啁啾
TDp = 50;               % fs, intensity FWHM
TDe = TDp * sqrt(2);    % fs, amplitude FWHM
coeff = 15;             % 截断系数
WWp = sqrt(8*log(2))/TDp * 5;
stWW = WWp/200;
NWW = 2*round(WWp/stWW)+1;
WDp = wlam/2.38;        % nm, ~2.38 eV
wp = 2.38;              % eV
Nfi = 3;                % 相位循环
FiAm = 0.009843;        % eV, ~5 μJ/cm^2

%% ================== 三、系统哈密顿量 ==================
N = 2; NN = 4;          % 两个二能级，4态
E1 = 19032; E2 = 19355; % 2.36 eV, 2.40 eV (cm^-1)
Omega = 0.01 / CmeV;    % 耦合 0.01 eV
H0 = zeros(NN,NN);
H0(2,2) = E1; H0(3,3) = E2; H0(4,4) = E1 + E2;
H0(2,3) = Omega; H0(3,2) = Omega;
H0 = H0 * CmeV;         % 转为 eV
Z = eye(NN,NN);
H0 = H0 - Z * wp;       % 旋转参考系
Mu = 6;                 % Debye
X = zeros(NN,NN);
X(1,2) = Mu; X(1,3) = Mu; X(2,4) = Mu; X(3,4) = Mu;
Xd = X';

%% ================== 四、耗散参数 ==================
gamma = 0.001;          % ps^-1
gamma_phi = 0.02;       % ps^-1, T2* ≈ 50 ps

%% ================== 五、时间参数 ==================
max_tau12 = 50;         % 100 fs
max_tau34 = 50;
step_tau = 2;           % fs
maxT = 1;
TT = 10;                % ps
Delta_t = 0.2;          % fs
N_shots = 100;          % 逐次次数

%% ================== 六、初始化 ==================
Nfi3 = Nfi^3;
Delta_tR = Delta_t * dh;
fi1 = 0; fi2 = 0; fi3 = 0; fi4 = 0;
F1 = FiAm; F2 = FiAm; F3 = FiAm; F4 = FiAm;
Rho = zeros(NN,NN); Rho(1,1) = 1;
ttau12 = (0:max_tau12-1)' * step_tau;
ttau34 = (0:max_tau34-1)' * step_tau;
I_avg = zeros(max_tau12, max_tau34);
I_var = zeros(max_tau12, max_tau34);
Pop_ex = zeros(round(8/Delta_t)+1, maxT); % 8 ps
Ark = zeros(4,4); Brk = zeros(4,1); Crk = zeros(4,1);
Ark(2,1) = 0.5; Ark(3,2) = 0.5; Ark(4,3) = 1;
Brk(1) = 1/6; Brk(2) = 2/6; Brk(3) = 2/6; Brk(4) = 1/6;
Crk(2) = 0.5; Crk(3) = 0.5; Crk(4) = 1;

%% ================== 七、单独计算时间域布居 ==================
disp('Computing time-domain population...');
T = TT;
tau12 = 0; tau34 = 0;
tau1 = -tau12 - T - tau34;
tau2 = -T - tau34;
tau3 = -tau34;
tau4 = 0;
tau0 = -coeff * TDe - tau12 - T - tau34;
maxJ = round((2 * coeff * TDe + max_tau12 * step_tau + ...
    max_tau34 * step_tau + TT) / Delta_t) + 1;
maxJ2 = 2 * maxJ + 3;

% 脉冲包络
Pulse1 = zeros(maxJ2,1); Pulse2 = Pulse1;
Pulse3 = Pulse1; Pulse4 = Pulse1;
TRK = zeros(maxJ2,1);
for jt2 = 1:maxJ2
    tt2 = tau0 + (jt2-1) * Delta_t / 2;
    TRK(jt2) = tt2;
    A1 = abs(tt2 - tau1) <= coeff * TDe;
    A2 = abs(tt2 - tau2) <= coeff * TDe;
    A3 = abs(tt2 - tau3) <= coeff * TDe;
    A4 = abs(tt2 - tau4) <= coeff * TDe;
    for iw = 1:NWW
        WW = (iw-1)*stWW - WWp;
        EW = exp(-TDp^2/(8*log(2))*WW^2);
        Pulse1(jt2) = Pulse1(jt2) + A1 * EW * exp(1i*WW*(tt2-tau1));
        Pulse2(jt2) = Pulse2(jt2) + A2 * EW * exp(1i*WW*(tt2-tau2));
        Pulse3(jt2) = Pulse3(jt2) + A3 * EW * exp(1i*WW*(tt2-tau3));
        Pulse4(jt2) = Pulse4(jt2) + A4 * EW * exp(1i*WW*(tt2-tau4));
    end
end
Pu1 = max(real(Pulse1)); Pulse1 = Pulse1 / Pu1;
Pu2 = max(real(Pulse2)); Pulse2 = Pulse2 / Pu2;
Pu3 = max(real(Pulse3)); Pulse3 = Pulse3 / Pu3;
Pu4 = max(real(Pulse4)); Pulse4 = Pulse4 / Pu4;

% 时间演化
Rho_t = Rho;
tm = zeros(maxJ,1);
for jt = 1:min(maxJ, round(8/Delta_t)+1)
    tm(jt) = tau0 + (jt-1) * Delta_t;
    tc = tm(jt);
    g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
    g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
    phase1 = (wp*tc)*dh - fi1;
    phase2 = (wp*tc)*dh - fi2;
    phase3 = (wp*tc)*dh - fi3;
    phase4 = (wp*tc)*dh - fi4;
    FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
         exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
    FXd = conj(FX);
    Ht = H0 - X * FX - Xd * FXd;
    
    K1 = -1i * (Ht * Rho_t - Rho_t * Ht);
    for i = 1:N
        sig_minus = zeros(NN,NN);
        if i == 1
            sig_minus(1,2) = 1;
        else
            sig_minus(1,3) = 1;
        end
        sig_plus = sig_minus';
        sig_z = sig_plus * sig_minus;
        K1 = K1 + gamma * (sig_minus * Rho_t * sig_plus - ...
            0.5 * (sig_plus * sig_minus * Rho_t + Rho_t * sig_plus * sig_minus)) + ...
            gamma_phi * (sig_z * Rho_t * sig_z - ...
            0.5 * (sig_z * sig_z * Rho_t + Rho_t * sig_z * sig_z));
    end
    
    Rho_temp = Rho_t + Delta_tR * Ark(2,1) * K1;
    tc_temp = tc + Delta_t * Crk(2);
    g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
    g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
    phase1 = (wp*tc_temp)*dh - fi1;
    phase2 = (wp*tc_temp)*dh - fi2;
    phase3 = (wp*tc_temp)*dh - fi3;
    phase4 = (wp*tc_temp)*dh - fi4;
    FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
         exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
    FXd = conj(FX);
    Ht = H0 - X * FX - Xd * FXd;
    K2 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
    for i = 1:N
        sig_minus = zeros(NN,NN);
        if i == 1
            sig_minus(1,2) = 1;
        else
            sig_minus(1,3) = 1;
        end
        sig_plus = sig_minus';
        sig_z = sig_plus * sig_minus;
        K2 = K2 + gamma * (sig_minus * Rho_temp * sig_plus - ...
            0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
            gamma_phi * (sig_z * Rho_temp * sig_z - ...
            0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
    end
    
    Rho_temp = Rho_t + Delta_tR * (Ark(3,1)*K1 + Ark(3,2)*K2);
    tc_temp = tc + Delta_t * Crk(3);
    g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
    g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
    phase1 = (wp*tc_temp)*dh - fi1;
    phase2 = (wp*tc_temp)*dh - fi2;
    phase3 = (wp*tc_temp)*dh - fi3;
    phase4 = (wp*tc_temp)*dh - fi4;
    FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
         exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
    FXd = conj(FX);
    Ht = H0 - X * FX - Xd * FXd;
    K3 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
    for i = 1:N
        sig_minus = zeros(NN,NN);
        if i == 1
            sig_minus(1,2) = 1;
        else
            sig_minus(1,3) = 1;
        end
        sig_plus = sig_minus';
        sig_z = sig_plus * sig_minus;
        K3 = K3 + gamma * (sig_minus * Rho_temp * sig_plus - ...
            0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
            gamma_phi * (sig_z * Rho_temp * sig_z - ...
            0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
    end
    
    Rho_temp = Rho_t + Delta_tR * (Ark(4,1)*K1 + Ark(4,2)*K2 + Ark(4,3)*K3);
    tc_temp = tc + Delta_t * Crk(4);
    g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
    g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
    phase1 = (wp*tc_temp)*dh - fi1;
    phase2 = (wp*tc_temp)*dh - fi2;
    phase3 = (wp*tc_temp)*dh - fi3;
    phase4 = (wp*tc_temp)*dh - fi4;
    FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
         exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
    FXd = conj(FX);
    Ht = H0 - X * FX - Xd * FXd;
    K4 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
    for i = 1:N
        sig_minus = zeros(NN,NN);
        if i == 1
            sig_minus(1,2) = 1;
        else
            sig_minus(1,3) = 1;
        end
        sig_plus = sig_minus';
        sig_z = sig_plus * sig_minus;
        K4 = K4 + gamma * (sig_minus * Rho_temp * sig_plus - ...
            0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
            gamma_phi * (sig_z * Rho_temp * sig_z - ...
            0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
    end
    
    Rho_t = Rho_t + Delta_tR * (Brk(1)*K1 + Brk(2)*K2 + Brk(3)*K3 + Brk(4)*K4);
    Pop_ex(jt, 1) = real(Rho_t(2,2) + Rho_t(3,3) + 2*Rho_t(4,4));
end

%% ================== 八、主循环：并行FR-2D ==================
disp(['Computing FR-2D, T = ', num2str(T), ' ps']);
I_shots = zeros(max_tau12, max_tau34, N_shots);

parfor itau12 = 1:max_tau12
    tau12 = (itau12-1) * step_tau;
    I_shots_temp = zeros(max_tau34, N_shots);
    
    for itau34 = 1:max_tau34
        tau34 = (itau34-1) * step_tau;
        
        % 脉冲时间
        tau1 = -tau12 - T - tau34;
        tau2 = -T - tau34;
        tau3 = -tau34;
        tau4 = 0;
        
        % 时间范围
        tau0 = -coeff * TDe - tau12 - T - tau34;
        maxJ = round((2 * coeff * TDe + max_tau12 * step_tau + ...
            max_tau34 * step_tau + TT) / Delta_t) + 1;
        maxJ2 = 2 * maxJ + 3;
        
        % 脉冲包络
        Pulse1 = zeros(maxJ2,1); Pulse2 = Pulse1;
        Pulse3 = Pulse1; Pulse4 = Pulse1;
        TRK = zeros(maxJ2,1);
        
        for jt2 = 1:maxJ2
            tt2 = tau0 + (jt2-1) * Delta_t / 2;
            TRK(jt2) = tt2;
            A1 = abs(tt2 - tau1) <= coeff * TDe;
            A2 = abs(tt2 - tau2) <= coeff * TDe;
            A3 = abs(tt2 - tau3) <= coeff * TDe;
            A4 = abs(tt2 - tau4) <= coeff * TDe;
            
            for iw = 1:NWW
                WW = (iw-1)*stWW - WWp;
                EW = exp(-TDp^2/(8*log(2))*WW^2);
                Pulse1(jt2) = Pulse1(jt2) + A1 * EW * exp(1i*WW*(tt2-tau1));
                Pulse2(jt2) = Pulse2(jt2) + A2 * EW * exp(1i*WW*(tt2-tau2));
                Pulse3(jt2) = Pulse3(jt2) + A3 * EW * exp(1i*WW*(tt2-tau3));
                Pulse4(jt2) = Pulse4(jt2) + A4 * EW * exp(1i*WW*(tt2-tau4));
            end
        end
        
        % 归一化
        Pu1 = max(real(Pulse1)); Pulse1 = Pulse1 / Pu1;
        Pu2 = max(real(Pulse2)); Pulse2 = Pulse2 / Pu2;
        Pu3 = max(real(Pulse3)); Pulse3 = Pulse3 / Pu3;
        Pu4 = max(real(Pulse4)); Pulse4 = Pulse4 / Pu4;
        
        % 逐次模拟
        I_temp = zeros(N_shots,1);
        for n = 1:N_shots
            Rho_n = zeros(NN,NN); Rho_n(1,1) = 1;
            if n > 1
                epsilon = 0.1; % 增加扰动
                Rho_rand = rand(NN,NN) + 1i*rand(NN,NN);
                Rho_rand = Rho_rand + Rho_rand';
                Rho_rand = Rho_rand / trace(Rho_rand);
                Rho_n = (1-epsilon) * Rho_n + epsilon * Rho_rand;
            end
            
            Rho_t = Rho_n;
            tm = zeros(maxJ,1);
            for jt = 1:maxJ
                tm(jt) = tau0 + (jt-1) * Delta_t;
                tc = tm(jt);
                
                g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
                g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
                phase1 = (wp*tc)*dh - fi1;
                phase2 = (wp*tc)*dh - fi2;
                phase3 = (wp*tc)*dh - fi3;
                phase4 = (wp*tc)*dh - fi4;
                FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
                     exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
                FXd = conj(FX);
                Ht = H0 - X * FX - Xd * FXd;
                
                K1 = -1i * (Ht * Rho_t - Rho_t * Ht);
                for i = 1:N
                    sig_minus = zeros(NN,NN);
                    if i == 1
                        sig_minus(1,2) = 1;
                    else
                        sig_minus(1,3) = 1;
                    end
                    sig_plus = sig_minus';
                    sig_z = sig_plus * sig_minus;
                    K1 = K1 + gamma * (sig_minus * Rho_t * sig_plus - ...
                        0.5 * (sig_plus * sig_minus * Rho_t + Rho_t * sig_plus * sig_minus)) + ...
                        gamma_phi * (sig_z * Rho_t * sig_z - ...
                        0.5 * (sig_z * sig_z * Rho_t + Rho_t * sig_z * sig_z));
                end
                
                Rho_temp = Rho_t + Delta_tR * Ark(2,1) * K1;
                tc_temp = tc + Delta_t * Crk(2);
                g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
                g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
                phase1 = (wp*tc_temp)*dh - fi1;
                phase2 = (wp*tc_temp)*dh - fi2;
                phase3 = (wp*tc_temp)*dh - fi3;
                phase4 = (wp*tc_temp)*dh - fi4;
                FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
                     exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
                FXd = conj(FX);
                Ht = H0 - X * FX - Xd * FXd;
                K2 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
                for i = 1:N
                    sig_minus = zeros(NN,NN);
                    if i == 1
                        sig_minus(1,2) = 1;
                    else
                        sig_minus(1,3) = 1;
                    end
                    sig_plus = sig_minus';
                    sig_z = sig_plus * sig_minus;
                    K2 = K2 + gamma * (sig_minus * Rho_temp * sig_plus - ...
                        0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
                        gamma_phi * (sig_z * Rho_temp * sig_z - ...
                        0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
                end
                
                Rho_temp = Rho_t + Delta_tR * (Ark(3,1)*K1 + Ark(3,2)*K2);
                tc_temp = tc + Delta_t * Crk(3);
                g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
                g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
                phase1 = (wp*tc_temp)*dh - fi1;
                phase2 = (wp*tc_temp)*dh - fi2;
                phase3 = (wp*tc_temp)*dh - fi3;
                phase4 = (wp*tc_temp)*dh - fi4;
                FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
                     exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
                FXd = conj(FX);
                Ht = H0 - X * FX - Xd * FXd;
                K3 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
                for i = 1:N
                    sig_minus = zeros(NN,NN);
                    if i == 1
                        sig_minus(1,2) = 1;
                    else
                        sig_minus(1,3) = 1;
                    end
                    sig_plus = sig_minus';
                    sig_z = sig_plus * sig_minus;
                    K3 = K3 + gamma * (sig_minus * Rho_temp * sig_plus - ...
                        0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
                        gamma_phi * (sig_z * Rho_temp * sig_z - ...
                        0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
                end
                
                Rho_temp = Rho_t + Delta_tR * (Ark(4,1)*K1 + Ark(4,2)*K2 + Ark(4,3)*K3);
                tc_temp = tc + Delta_t * Crk(4);
                g1 = F1 * Pulse1(2*jt-1); g2 = F2 * Pulse2(2*jt-1);
                g3 = F3 * Pulse3(2*jt-1); g4 = F4 * Pulse4(2*jt-1);
                phase1 = (wp*tc_temp)*dh - fi1;
                phase2 = (wp*tc_temp)*dh - fi2;
                phase3 = (wp*tc_temp)*dh - fi3;
                phase4 = (wp*tc_temp)*dh - fi4;
                FX = exp(1i*phase1)*g1 + exp(1i*phase2)*g2 + ...
                     exp(1i*phase3)*g3 + exp(1i*phase4)*g4;
                FXd = conj(FX);
                Ht = H0 - X * FX - Xd * FXd;
                K4 = -1i * (Ht * Rho_temp - Rho_temp * Ht);
                for i = 1:N
                    sig_minus = zeros(NN,NN);
                    if i == 1
                        sig_minus(1,2) = 1;
                    else
                        sig_minus(1,3) = 1;
                    end
                    sig_plus = sig_minus';
                    sig_z = sig_plus * sig_minus;
                    K4 = K4 + gamma * (sig_minus * Rho_temp * sig_plus - ...
                        0.5 * (sig_plus * sig_minus * Rho_temp + Rho_temp * sig_plus * sig_minus)) + ...
                        gamma_phi * (sig_z * Rho_temp * sig_z - ...
                        0.5 * (sig_z * sig_z * Rho_temp + Rho_t * sig_z * sig_z));
                end
                
                Rho_t = Rho_t + Delta_tR * (Brk(1)*K1 + Brk(2)*K2 + Brk(3)*K3 + Brk(4)*K4);
            end
            
            W = 8;  % 检测窗口 8 ps
            t_det = tau4;
            I_temp(n) = 0;
            for jt = round(t_det/Delta_t)+1 : round((t_det+W)/Delta_t)+1
                I_temp(n) = I_temp(n) + gamma * real(Rho_t(2,2) + Rho_t(3,3));
            end
            I_shots_temp(itau34, n) = I_temp(n);
        end
        I_shots(itau12, :, :) = I_shots_temp;
    end
end

% 计算均值和方差
for itau12 = 1:max_tau12
    for itau34 = 1:max_tau34
        I_avg(itau12, itau34) = mean(I_shots(itau12, itau34, :));
        I_var(itau12, itau34) = var(I_shots(itau12, itau34, :));
    end
end

%% ================== 九、2D光谱计算 ==================
max_Wtau = 80; max_Wt = 80;
Del = 0.05;
step_Wt = 2*Del / max_Wt;
Wt0 = wp - Del;
Wtau0 = wp - Del;
f2 = 4*log(2);
fw12 = sqrt(-f2 * (max_tau12*step_tau)^2 / log(0.05));
fw34 = sqrt(-f2 * (max_tau34*step_tau)^2 / log(0.05));
C12 = f2 / (fw12^2 * 2);
C34 = f2 / (fw34^2 * 2);
Stauw_avg = zeros(max_tau12, max_Wt);
Sww_avg = zeros(max_Wtau, max_Wt);
Stauw_var = zeros(max_tau12, max_Wt);
Sww_var = zeros(max_Wtau, max_Wt);
Wt = Wt0 + (0:max_Wt-1)' * step_Wt;
Wtau = Wtau0 + (0:max_Wtau-1)' * step_Wt;

for j1 = 1:max_tau12
    for j3w = 1:max_Wt
        for j3 = 1:max_tau34
            Wind = exp(-C34 * ttau34(j3)^2);
            fi = 1.52 * ttau34(j3) * Wt(j3w);
            Stauw_avg(j1, j3w) = Stauw_avg(j1, j3w) + ...
                I_avg(j1, j3) * exp(1i*fi) * Wind;
            Stauw_var(j1, j3w) = Stauw_var(j1, j3w) + ...
                I_var(j1, j3) * exp(1i*fi) * Wind;
        end
    end
end

for j3w = 1:max_Wt
    for j1w = 1:max_Wtau
        for j1 = 1:max_tau12
            Wind = exp(-C12 * ttau12(j1)^2);
            fi = ttau12(j1) * Wtau(j1w) * 1.52;
            Sww_avg(j1w, j3w) = Sww_avg(j1w, j3w) + ...
                Stauw_avg(j1, j3w) * exp(-1i*fi) * Wind;
            Sww_var(j1w, j3w) = Sww_var(j1w, j3w) + ...
                Stauw_var(j1, j3w) * exp(-1i*fi) * Wind;
        end
    end
end

Sp2D_avg = -real(Sww_avg);
Sp2D_var = real(Sww_var);

%% ================== 十、可视化 ==================
figure;
plot((0:Delta_t:8), Pop_ex(:,1), 'r');
xlabel('Time (ps)');
ylabel('Excited Population');
title('SF Burst');
grid on;

figure;
surf(Wtau, Wt, Sp2D_avg', 'LineStyle', 'none');
view(0,90);
xlim([2.31, 2.45]); ylim([2.31, 2.45]);
xlabel('\omega_{\tau} (eV)');
ylabel('\omega_{t} (eV)');
title('FR-2D: I_{avg}');
colorbar;
hold on;
contour(Wtau, Wt, Sp2D_avg', 'LineColor', 'k'); % 添加等高线

figure;
R = Sp2D_var ./ (Sp2D_avg.^2 + eps);
surf(Wtau, Wt, R', 'LineStyle', 'none');
view(0,90);
xlim([2.31, 2.45]); ylim([2.31, 2.45]);
xlabel('\omega_{\tau} (eV)');
ylabel('\omega_{t} (eV)');
title('Normalized Variance R');
colorbar;
hold on;
contour(Wtau, Wt, R', 'LineColor', 'k');

figure;
surf(Wtau, Wt, Sp2D_var', 'LineStyle', 'none');
view(0,90);
xlim([2.31, 2.45]); ylim([2.31, 2.45]);
xlabel('\omega_{\tau} (eV)');
ylabel('\omega_{t} (eV)');
title('FR-2D: I_{var}');
colorbar;

%% ================== 十一、保存 ==================
save('FR2D_CsPbBr3_Fast.mat', 'Sp2D_avg', 'Sp2D_var', 'R', ...
    'Wtau', 'Wt', 'Pop_ex', 'ttau12', 'ttau34', '-v7.3');

delete(gcp('nocreate')); % 关闭并行池
toc;